import { ApiListResponse, ApiResponse } from "../types/api";
import { AdjustDetailItem, AdjustDetailParams, ChargeAdjustItem, ChargeAdjustParams, AdjustAccountRequest } from "../types/adjustDetail";
import api from "./api";

// 获取调账记录列表
export const getAdjustDetails = async (
  params: AdjustDetailParams
): Promise<ApiListResponse<AdjustDetailItem[]>> => {
  const response = await api.get("/adjust-details", { params });
  return response.data;
};

// 导出调账明细列表
export const exportAdjustDetails = async (params: AdjustDetailParams): Promise<{
  blob: Blob;
  filename: string;
}> => {
  // 过滤空值
  const filteredParams = { ...params };
  Object.keys(filteredParams).forEach((key) => {
    if (filteredParams[key] === "" || filteredParams[key] === null) {
      delete filteredParams[key];
    }
  });

  const response = await api.get("/adjust-details/export", {
    params: filteredParams,
    responseType: "blob",
  });

  // 从响应头中获取文件名
  const contentDisposition = response.headers["content-disposition"];
  let filename = "调账明细.xlsx";

  if (contentDisposition) {
    const filenameMatch = contentDisposition.match(/filename="(.+?)"/);
    if (filenameMatch && filenameMatch[1]) {
      try {
        filename = decodeURIComponent(filenameMatch[1]);
      } catch (error) {
        console.error("Error decoding filename:", error);
      }
    }
  }

  return {
    blob: response.data,
    filename: filename,
  };
};

// 获取权责调账列表
export const getChargeAdjustList = async (
  params: ChargeAdjustParams
): Promise<ApiListResponse<ChargeAdjustItem[]>> => {
  const response = await api.get("/charge-adjust", { params });
  return response.data;
};

// 调账操作
export const adjustAccount = async (
  data: AdjustAccountRequest
): Promise<ApiResponse<any>> => {
  const response = await api.post("/charge-adjust/adjust-account", data);
  return response.data;
};
