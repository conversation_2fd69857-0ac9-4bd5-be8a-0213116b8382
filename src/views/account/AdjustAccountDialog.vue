<script setup lang="ts">
import { ref, watch, onMounted } from "vue";
import { useToast } from "primevue/usetoast";
import type {
  ChargeAdjustItem,
  AdjustAccountRequest,
} from "../../types/adjustDetail";
import { adjustAccount } from "../../services/adjustDetail";
import { optionLoaders } from "../../utils/options";

const props = defineProps<{
  visible: boolean;
  chargeAdjustItem: ChargeAdjustItem | null;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "success"): void;
}>();

const toast = useToast();
const dialogVisible = ref(false);
const loading = ref(false);
const submitted = ref(false);

// 表单数据
const formData = ref<AdjustAccountRequest>({
  batch_no: "",
  order_no: "",
  sub_order_no: "",
  charge_month: 0,
  adjust_month: 0,
  adjust_amount: 0,
  adjust_tax: 0,
  adjust_reason_class: "",
  adjust_reason: "",
  adjust_charge_detail_id: 0,
});

// 日期选择器的值
const chargeMonthDate = ref<Date | null>(null);

// 监听props变化
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal;
    if (newVal && props.chargeAdjustItem) {
      initFormData();
    }
  }
);

// 监听内部状态变化
watch(dialogVisible, (newVal) => {
  emit("update:visible", newVal);
  if (!newVal) {
    resetForm();
  }
});

// 初始化表单数据
const initFormData = () => {
  if (props.chargeAdjustItem) {
    const item = props.chargeAdjustItem;

    formData.value = {
      batch_no: item.batch_no,
      order_no: item.order_no,
      sub_order_no: item.sub_order_no,
      charge_month: 0,
      adjust_month: item.charge_month, // 默认为当前权责的charge_month
      adjust_amount: 0,
      adjust_tax: item.tax || 0,
      adjust_reason_class: "",
      adjust_reason: "",
      adjust_charge_detail_id: item.id,
    };
  }
};

// 重置表单
const resetForm = () => {
  formData.value = {
    batch_no: "",
    order_no: "",
    sub_order_no: "",
    charge_month: 0,
    adjust_month: 0,
    adjust_amount: 0,
    adjust_tax: 0,
    adjust_reason_class: "",
    adjust_reason: "",
    adjust_charge_detail_id: 0,
  };
  chargeMonthDate.value = null;
  submitted.value = false;
};

// 监听日期选择器变化
watch(chargeMonthDate, (newDate) => {
  if (newDate) {
    const year = newDate.getFullYear();
    const month = String(newDate.getMonth() + 1).padStart(2, "0");
    formData.value.charge_month = parseInt(`${year}${month}`);
  }
});

// 加载调账原因分类选项
const adjustReasonClassOptions = ref<{ label: string; value: string }[]>([]);
const loadAdjustReasonClassOptions = async () =>
  optionLoaders.signContractEntity(adjustReasonClassOptions);

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false;
};

// 提交表单
const submitForm = async () => {
  submitted.value = true;

  // 表单验证
  if (!formData.value.adjust_amount || formData.value.adjust_amount === 0) {
    toast.add({
      severity: "error",
      summary: "验证错误",
      detail: "请输入调账金额",
      life: 3000,
    });
    return;
  }

  if (!formData.value.adjust_reason_class) {
    toast.add({
      severity: "error",
      summary: "验证错误",
      detail: "请选择调账原因分类",
      life: 3000,
    });
    return;
  }

  if (!formData.value.adjust_reason) {
    toast.add({
      severity: "error",
      summary: "验证错误",
      detail: "请输入调账原因",
      life: 3000,
    });
    return;
  }

  try {
    loading.value = true;

    // 确保必要字段不为空
    if (!formData.value.charge_month || !formData.value.adjust_month) {
      toast.add({
        severity: "error",
        summary: "验证错误",
        detail: "日期字段不能为空",
        life: 3000,
      });
      return;
    }

    // 提交数据，直接使用YYMM格式
    const submitData: AdjustAccountRequest = {
      batch_no: formData.value.batch_no,
      order_no: formData.value.order_no,
      sub_order_no: formData.value.sub_order_no,
      charge_month: formData.value.charge_month,
      adjust_month: formData.value.adjust_month,
      adjust_amount: formData.value.adjust_amount,
      adjust_tax: formData.value.adjust_tax,
      adjust_reason_class: formData.value.adjust_reason_class,
      adjust_reason: formData.value.adjust_reason,
      adjust_charge_detail_id: formData.value.adjust_charge_detail_id,
    };

    await adjustAccount(submitData);

    toast.add({
      severity: "success",
      summary: "成功",
      detail: "调账操作成功",
      life: 3000,
    });

    emit("success");
    closeDialog();
  } catch (error: any) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: error.response?.data?.message || "调账操作失败",
      life: 3000,
    });
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  loadAdjustReasonClassOptions();
});
</script>

<template>
  <Dialog
    v-model:visible="dialogVisible"
    modal
    :header="`调账操作 - ${chargeAdjustItem?.order_no || ''}`"
    :style="{ width: '50rem' }"
    class="apple-adjust-dialog"
    :closable="true"
    :dismissableMask="true"
    @hide="closeDialog"
  >
    <div v-if="chargeAdjustItem" class="adjust-form-content">
      <form @submit.prevent="submitForm">
        <Fluid>
          <div class="grid grid-cols-2 gap-4">
            <div class="form-field">
              <FloatLabel>
                <InputText
                  v-model="formData.batch_no"
                  disabled
                  class="w-full"
                />
                <label>批次号</label>
              </FloatLabel>
            </div>
            <div class="form-field">
              <FloatLabel>
                <InputText
                  v-model="formData.order_no"
                  disabled
                  class="w-full"
                />
                <label>订单编号</label>
              </FloatLabel>
            </div>
            <div class="form-field">
              <FloatLabel>
                <InputText
                  v-model="formData.sub_order_no"
                  disabled
                  class="w-full"
                />
                <label>子订单编号</label>
              </FloatLabel>
            </div>
            <div class="form-field">
              <FloatLabel>
                <InputNumber
                  v-model="formData.adjust_month"
                  disabled
                  :useGrouping="false"
                  class="w-full"
                />
                <label>调整账期</label>
              </FloatLabel>
            </div>
            <div class="form-field">
              <FloatLabel>
                <InputNumber
                  v-model="formData.adjust_tax"
                  disabled
                  suffix="%"
                  :maxFractionDigits="2"
                  class="w-full"
                />
                <label>调账税率</label>
              </FloatLabel>
            </div>
            <!-- 账期 -->
            <div class="form-field">
              <FloatLabel>
                <DatePicker
                  v-model="chargeMonthDate"
                  view="month"
                  dateFormat="yymm"
                  showIcon
                  class="w-full"
                />
                <label>账期</label>
              </FloatLabel>
            </div>
            <!-- 调账金额 -->
            <div class="form-field">
              <FloatLabel>
                <InputNumber
                  v-model="formData.adjust_amount"
                  :min="0"
                  :maxFractionDigits="2"
                  class="w-full"
                  :class="{
                    'p-invalid':
                      submitted &&
                      (!formData.adjust_amount || formData.adjust_amount === 0),
                  }"
                  showButtons
                />
                <label>调账金额 *</label>
              </FloatLabel>
            </div>
            <!-- 调账分类 -->
            <div class="form-field">
              <FloatLabel>
                <Select
                  v-model="formData.adjust_reason_class"
                  :options="adjustReasonClassOptions"
                  optionLabel="label"
                  optionValue="value"
                  class="w-full"
                  :class="{
                    'p-invalid': submitted && !formData.adjust_reason_class,
                  }"
                />
                <label>调账原因分类 *</label>
              </FloatLabel>
            </div>
          </div>
          <!-- 调账原因 -->
          <div class="form-field mt-4">
            <FloatLabel>
              <Textarea
                v-model="formData.adjust_reason"
                rows="3"
                class="w-full"
                :class="{ 'p-invalid': submitted && !formData.adjust_reason }"
              />
              <label>调账原因 *</label>
            </FloatLabel>
          </div>
        </Fluid>
      </form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <Button
          label="取消"
          icon="pi pi-times"
          severity="secondary"
          outlined
          @click="closeDialog"
          class="apple-button mr-2"
        />
        <Button
          label="确认调账"
          icon="pi pi-check"
          @click="submitForm"
          :loading="loading"
          class="apple-button"
        />
      </div>
    </template>
  </Dialog>
</template>

<style scoped>
/* Apple Design System - 调账弹框样式 */
:deep(.apple-adjust-dialog) {
  border-radius: 16px !important;
  overflow: hidden !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15) !important;
}

:deep(.apple-adjust-dialog .p-dialog-header) {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08) !important;
  padding: 1.5rem 2rem !important;
  border-radius: 16px 16px 0 0 !important;
}

:deep(.apple-adjust-dialog .p-dialog-header .p-dialog-title) {
  font-size: 1.25rem !important;
  font-weight: 600 !important;
  color: #1d1d1f !important;
}

:deep(.apple-adjust-dialog .p-dialog-content) {
  padding: 2rem !important;
  background: #ffffff !important;
}

.adjust-form-content {
  max-height: 60vh;
  overflow-y: auto;
}

.form-field {
  margin-bottom: 1.5rem;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.apple-button {
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.apple-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 表单验证样式 */
:deep(.p-invalid) {
  border-color: #ef4444 !important;
}

:deep(.p-invalid:focus) {
  border-color: #ef4444 !important;
  box-shadow: 0 0 0 0.2rem rgba(239, 68, 68, 0.2) !important;
}
</style>
